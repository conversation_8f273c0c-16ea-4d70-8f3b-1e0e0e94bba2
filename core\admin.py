from django.contrib import admin
from django.contrib.auth import get_user_model
from django.utils.html import format_html
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from .models import Site, Equipment, MaintenanceLog, Incident, Document

User = get_user_model()


@admin.register(Site)
class SiteAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'country', 'is_active', 'created_at')
    list_filter = ('is_active', 'country', 'city')
    search_fields = ('name', 'address', 'city', 'postal_code')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('Site Information'), {
            'fields': ('name', 'is_active')
        }),
        (_('Address'), {
            'fields': ('address', 'postal_code', 'city', 'country')
        }),
        (_('Contact Information'), {
            'fields': ('contact_person', 'contact_phone', 'contact_email', 'notes')
        }),
        (_('Technical Details'), {
            'fields': ('surface_area', 'building_type', 'construction_year', 
                      'latitude', 'longitude')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class MaintenanceLogInline(admin.TabularInline):
    model = MaintenanceLog
    extra = 0
    fields = ('maintenance_type', 'performed_date', 'performed_by', 'description', 'is_completed')
    readonly_fields = ('performed_by',)
    
    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only set added during the first save.
            obj.performed_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'equipment_type', 'model', 'site', 'status', 'installation_date', 'warranty_status')
    list_filter = ('equipment_type', 'status', 'manufacturer', 'site')
    search_fields = ('name', 'model', 'serial_number', 'manufacturer')
    list_select_related = ('site',)
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'installation_date'
    inlines = [MaintenanceLogInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'equipment_type', 'model', 'manufacturer', 'serial_number', 'site', 'location')
        }),
        (_('Status & Dates'), {
            'fields': ('status', 'installation_date', 'warranty_expiry', 'last_maintenance', 'next_maintenance')
        }),
        (_('Network Information'), {
            'fields': ('ip_address', 'mac_address'),
            'classes': ('collapse',)
        }),
        (_('Technical Specifications'), {
            'fields': ('power_consumption', 'operating_temperature', 'weight', 'dimensions'),
            'classes': ('collapse',)
        }),
        (_('Custom Fields'), {
            'fields': ('custom_field_1', 'custom_field_2', 'custom_field_3'),
            'classes': ('collapse',)
        }),
        (_('Notes'), {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def warranty_status(self, obj):
        if obj.warranty_expiry:
            from datetime import date
            if date.today() > obj.warranty_expiry:
                return format_html('<span style="color: red;">Expired</span>')
            return 'Active'
        return 'N/A'
    warranty_status.short_description = 'Warranty Status'
    warranty_status.admin_order_field = 'warranty_expiry'


@admin.register(MaintenanceLog)
class MaintenanceLogAdmin(admin.ModelAdmin):
    list_display = ('equipment', 'maintenance_type', 'performed_date', 'performed_by', 'is_completed')
    list_filter = ('maintenance_type', 'is_completed', 'performed_date')
    search_fields = ('equipment__name', 'description', 'actions_taken')
    date_hierarchy = 'performed_date'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Maintenance Details'), {
            'fields': ('equipment', 'maintenance_type', 'performed_date', 'performed_by', 'is_completed')
        }),
        (_('Maintenance Information'), {
            'fields': ('description', 'actions_taken', 'parts_replaced')
        }),
        (_('Cost & Time'), {
            'fields': ('cost', 'duration_minutes'),
            'classes': ('collapse',)
        }),
        (_('Next Maintenance'), {
            'fields': ('next_maintenance_date',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only set added during the first save.
            obj.performed_by = request.user
        super().save_model(request, obj, form, change)


class IncidentInline(admin.TabularInline):
    model = Incident.equipment.through
    extra = 0
    verbose_name = _('Related Incident')
    verbose_name_plural = _('Related Incidents')
    readonly_fields = ('incident_link',)
    
    def incident_link(self, instance):
        url = reverse('admin:core_incident_change', args=[instance.incident.id])
        return format_html('<a href="{}">{}</a>', url, instance.incident)
    incident_link.short_description = _('Incident')


@admin.register(Incident)
class IncidentAdmin(admin.ModelAdmin):
    list_display = ('title', 'site', 'severity', 'status', 'reported_date', 'assigned_to')
    list_filter = ('severity', 'status', 'reported_date', 'site')
    search_fields = ('title', 'description', 'resolution')
    date_hierarchy = 'reported_date'
    readonly_fields = ('created_at', 'updated_at', 'reported_date', 'resolution_time_display')
    filter_horizontal = ('equipment',)
    
    fieldsets = (
        (_('Incident Information'), {
            'fields': ('title', 'description', 'severity', 'status', 'site')
        }),
        (_('Assignment & Dates'), {
            'fields': ('reported_by', 'reported_date', 'assigned_to', 'resolved_date', 'resolution_time_display')
        }),
        (_('Resolution'), {
            'fields': ('resolution', 'downtime_minutes', 'cost')
        }),
        (_('Categorization'), {
            'fields': ('category', 'subcategory'),
            'classes': ('collapse',)
        }),
        (_('Related Equipment'), {
            'fields': ('equipment',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def resolution_time_display(self, obj):
        if obj.resolution_time:
            days = obj.resolution_time.days
            hours, remainder = divmod(obj.resolution_time.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            return f"{days}d {hours}h {minutes}m"
        return "N/A"
    resolution_time_display.short_description = 'Time to Resolution'
    
    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only set added during the first save.
            obj.reported_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'document_type', 'get_related_object', 'upload_date', 'uploaded_by')
    list_filter = ('document_type', 'upload_date')
    search_fields = ('title', 'description')
    date_hierarchy = 'upload_date'
    readonly_fields = ('upload_date', 'uploaded_by', 'created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('title', 'document_type', 'file', 'description')
        }),
        (_('Related To'), {
            'fields': ('site', 'equipment')
        }),
        (_('Upload Information'), {
            'fields': ('uploaded_by', 'upload_date')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_related_object(self, obj):
        if obj.site:
            url = reverse('admin:core_site_change', args=[obj.site.id])
            return format_html('<a href="{}">{}</a>', url, obj.site)
        elif obj.equipment:
            url = reverse('admin:core_equipment_change', args=[obj.equipment.id])
            return format_html('<a href="{}">{}</a>', url, obj.equipment)
        return "-"
    get_related_object.short_description = _('Related To')
    get_related_object.admin_order_field = 'site'
    
    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Only set added during the first save.
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
