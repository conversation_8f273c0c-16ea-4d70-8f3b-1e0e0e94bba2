from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings
import uuid


class TimeStampedModel(models.Model):
    """Abstract base class with self-updating created and modified fields."""
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        abstract = True
        ordering = ['-created_at']


class Site(TimeStampedModel):
    """Site or location where security systems are installed."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(_('site name'), max_length=100)
    address = models.TextField(_('address'))
    city = models.CharField(_('city'), max_length=100)
    postal_code = models.CharField(_('postal code'), max_length=20)
    country = models.Char<PERSON><PERSON>(_('country'), max_length=100, default='France')
    contact_person = models.CharField(_('contact person'), max_length=100, blank=True)
    contact_phone = models.Char<PERSON>ield(_('contact phone'), max_length=20, blank=True)
    contact_email = models.EmailField(_('contact email'), blank=True)
    notes = models.TextField(_('notes'), blank=True)
    is_active = models.BooleanField(_('is active'), default=True)
    
    # Geo coordinates
    latitude = models.DecimalField(_('latitude'), max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(_('longitude'), max_digits=9, decimal_places=6, null=True, blank=True)
    
    # Technical details
    surface_area = models.PositiveIntegerField(_('surface area (m²)'), null=True, blank=True)
    building_type = models.CharField(_('building type'), max_length=50, blank=True)
    construction_year = models.PositiveIntegerField(_('construction year'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('site')
        verbose_name_plural = _('sites')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Equipment(TimeStampedModel):
    """Base model for all security equipment."""
    EQUIPMENT_TYPES = [
        ('camera', _('Camera')),
        ('nvr', _('NVR')),
        ('fire_panel', _('Fire Panel')),
        ('smoke_detector', _('Smoke Detector')),
        ('manual_call_point', _('Manual Call Point')),
        ('switch', _('Network Switch')),
        ('router', _('Router')),
        ('access_point', _('Access Point')),
        ('speaker', _('Speaker')),
        ('amplifier', _('Amplifier')),
        ('other', _('Other')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(_('equipment name'), max_length=100)
    equipment_type = models.CharField(_('equipment type'), max_length=50, choices=EQUIPMENT_TYPES)
    model = models.CharField(_('model'), max_length=100)
    manufacturer = models.CharField(_('manufacturer'), max_length=100)
    serial_number = models.CharField(_('serial number'), max_length=100, unique=True, blank=True, null=True)
    installation_date = models.DateField(_('installation date'), null=True, blank=True)
    warranty_expiry = models.DateField(_('warranty expiry'), null=True, blank=True)
    status = models.CharField(_('status'), max_length=20, choices=[
        ('operational', _('Operational')),
        ('maintenance', _('Under Maintenance')),
        ('faulty', _('Faulty')),
        ('decommissioned', _('Decommissioned')),
    ], default='operational')
    site = models.ForeignKey(Site, on_delete=models.CASCADE, related_name='equipment', verbose_name=_('site'))
    location = models.CharField(_('location'), max_length=200, help_text=_('Physical location within the site'))
    ip_address = models.GenericIPAddressField(_('IP address'), protocol='both', blank=True, null=True)
    mac_address = models.CharField(_('MAC address'), max_length=17, blank=True, null=True)
    notes = models.TextField(_('notes'), blank=True)
    last_maintenance = models.DateField(_('last maintenance'), null=True, blank=True)
    next_maintenance = models.DateField(_('next maintenance'), null=True, blank=True)
    
    # Technical specifications (common fields)
    power_consumption = models.DecimalField(_('power consumption (W)'), max_digits=6, decimal_places=2, null=True, blank=True)
    operating_temperature = models.CharField(_('operating temperature'), max_length=50, blank=True)
    weight = models.DecimalField(_('weight (kg)'), max_digits=6, decimal_places=2, null=True, blank=True)
    dimensions = models.CharField(_('dimensions (WxHxD)'), max_length=50, blank=True)
    
    # Custom fields for extensibility
    custom_field_1 = models.CharField(_('custom field 1'), max_length=100, blank=True)
    custom_field_2 = models.CharField(_('custom field 2'), max_length=100, blank=True)
    custom_field_3 = models.CharField(_('custom field 3'), max_length=100, blank=True)
    
    class Meta:
        verbose_name = _('equipment')
        verbose_name_plural = _('equipment')
        ordering = ['site', 'equipment_type', 'name']
    
    def __str__(self):
        return f"{self.get_equipment_type_display()}: {self.name} ({self.site.name})"
    
    @property
    def is_under_warranty(self):
        if not self.warranty_expiry:
            return False
        from datetime import date
        return date.today() <= self.warranty_expiry
    
    @property
    def needs_maintenance(self):
        if not self.next_maintenance:
            return False
        from datetime import date
        return date.today() >= self.next_maintenance


class MaintenanceLog(TimeStampedModel):
    """Log for equipment maintenance activities."""
    MAINTENANCE_TYPES = [
        ('preventive', _('Preventive')),
        ('corrective', _('Corrective')),
        ('inspection', _('Inspection')),
        ('upgrade', _('Upgrade')),
        ('other', _('Other')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, related_name='maintenance_logs', verbose_name=_('equipment'))
    maintenance_type = models.CharField(_('maintenance type'), max_length=20, choices=MAINTENANCE_TYPES)
    performed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, verbose_name=_('performed by'))
    performed_date = models.DateField(_('performed date'))
    next_maintenance_date = models.DateField(_('next maintenance date'), null=True, blank=True)
    description = models.TextField(_('description'))
    actions_taken = models.TextField(_('actions taken'))
    parts_replaced = models.TextField(_('parts replaced'), blank=True)
    cost = models.DecimalField(_('cost'), max_digits=10, decimal_places=2, null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(_('duration (minutes)'), null=True, blank=True)
    is_completed = models.BooleanField(_('is completed'), default=True)
    
    class Meta:
        verbose_name = _('maintenance log')
        verbose_name_plural = _('maintenance logs')
        ordering = ['-performed_date']
    
    def __str__(self):
        return f"{self.get_maintenance_type_display()} - {self.equipment.name} ({self.performed_date})"


class Incident(TimeStampedModel):
    """Security or system incidents."""
    SEVERITY_LEVELS = [
        ('critical', _('Critical')),
        ('high', _('High')),
        ('medium', _('Medium')),
        ('low', _('Low')),
        ('info', _('Information')),
    ]
    
    STATUS_CHOICES = [
        ('reported', _('Reported')),
        ('in_progress', _('In Progress')),
        ('resolved', _('Resolved')),
        ('closed', _('Closed')),
        ('cancelled', _('Cancelled')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(_('title'), max_length=200)
    description = models.TextField(_('description'))
    severity = models.CharField(_('severity'), max_length=20, choices=SEVERITY_LEVELS, default='medium')
    status = models.CharField(_('status'), max_length=20, choices=STATUS_CHOICES, default='reported')
    reported_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, 
                                  related_name='reported_incidents', verbose_name=_('reported by'))
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='assigned_incidents', verbose_name=_('assigned to'))
    site = models.ForeignKey(Site, on_delete=models.CASCADE, related_name='incidents', verbose_name=_('site'))
    equipment = models.ManyToManyField(Equipment, related_name='incidents', blank=True, verbose_name=_('related equipment'))
    reported_date = models.DateTimeField(_('reported date'), auto_now_add=True)
    resolved_date = models.DateTimeField(_('resolved date'), null=True, blank=True)
    resolution = models.TextField(_('resolution'), blank=True)
    downtime_minutes = models.PositiveIntegerField(_('downtime (minutes)'), null=True, blank=True)
    cost = models.DecimalField(_('cost'), max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Custom fields for categorization
    category = models.CharField(_('category'), max_length=50, blank=True)
    subcategory = models.CharField(_('subcategory'), max_length=50, blank=True)
    
    class Meta:
        verbose_name = _('incident')
        verbose_name_plural = _('incidents')
        ordering = ['-reported_date']
    
    def __str__(self):
        return f"{self.title} ({self.get_severity_display()}) - {self.site.name}"
    
    def save(self, *args, **kwargs):
        # Auto-update resolved_date when status changes to resolved
        if self.status == 'resolved' and not self.resolved_date:
            from django.utils import timezone
            self.resolved_date = timezone.now()
        super().save(*args, **kwargs)
    
    @property
    def resolution_time(self):
        if self.resolved_date and self.reported_date:
            return self.resolved_date - self.reported_date
        return None


class Document(TimeStampedModel):
    """Documents related to sites, equipment, or other entities."""
    DOCUMENT_TYPES = [
        ('manual', _('User Manual')),
        ('specification', _('Technical Specification')),
        ('certificate', _('Certificate')),
        ('report', _('Report')),
        ('diagram', _('Diagram')),
        ('other', _('Other')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(_('title'), max_length=200)
    document_type = models.CharField(_('document type'), max_length=20, choices=DOCUMENT_TYPES, default='other')
    file = models.FileField(_('file'), upload_to='documents/%Y/%m/%d/')
    description = models.TextField(_('description'), blank=True)
    upload_date = models.DateTimeField(_('upload date'), auto_now_add=True)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, verbose_name=_('uploaded by'))
    
    # Relationships (only one of these should be set)
    site = models.ForeignKey(Site, on_delete=models.CASCADE, null=True, blank=True, related_name='documents', verbose_name=_('site'))
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, null=True, blank=True, related_name='documents', verbose_name=_('equipment'))
    
    class Meta:
        verbose_name = _('document')
        verbose_name_plural = _('documents')
        ordering = ['-upload_date']
    
    def __str__(self):
        return self.title
    
    def clean(self):
        from django.core.exceptions import ValidationError
        if self.site and self.equipment:
            raise ValidationError(_('A document can only be associated with either a site or equipment, not both.'))
        if not self.site and not self.equipment:
            raise ValidationError(_('A document must be associated with either a site or equipment.'))
