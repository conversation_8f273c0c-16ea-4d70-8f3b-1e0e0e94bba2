import os
import csv
import json
from datetime import datetime, date
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch


def generate_equipment_report(queryset, format='pdf'):
    """
    Generate a report of equipment in the specified format (pdf, xlsx, csv).
    
    Args:
        queryset: QuerySet of Equipment objects
        format: Output format ('pdf', 'xlsx', 'csv')
        
    Returns:
        str: Path to the generated report file
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'equipment_report_{timestamp}.{format}'
    filepath = os.path.join('reports', 'equipment', filename)
    
    # Ensure the directory exists
    dir_path = os.path.join(settings.MEDIA_ROOT, 'reports', 'equipment')
    os.makedirs(dir_path, exist_ok=True)
    
    if format == 'pdf':
        return _generate_equipment_pdf(queryset, filepath)
    elif format == 'xlsx':
        return _generate_equipment_excel(queryset, filepath)
    elif format == 'csv':
        return _generate_equipment_csv(queryset, filepath)
    else:
        raise ValueError(f"Unsupported format: {format}")


def _generate_equipment_pdf(queryset, filepath):
    """Generate a PDF report of equipment."""
    full_path = os.path.join(settings.MEDIA_ROOT, filepath)
    
    # Create the PDF document
    doc = SimpleDocTemplate(
        full_path,
        pagesize=letter,
        rightMargin=72, leftMargin=72,
        topMargin=72, bottomMargin=72
    )
    
    # Custom styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(
        name='Title',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=20,
        alignment=1  # Center
    ))
    styles.add(ParagraphStyle(
        name='Subtitle',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=20,
        alignment=1  # Center
    ))
    
    # Content
    elements = []
    
    # Title
    elements.append(Paragraph(_('Equipment Report'), styles['Title']))
    elements.append(Paragraph(
        _('Generated on {date}').format(date=timezone.now().strftime('%Y-%m-%d %H:%M:%S')), 
        styles['Subtitle']
    ))
    elements.append(Spacer(1, 20))
    
    # Summary
    summary_data = [
        [_('Total Equipment'), len(queryset)],
        [_('Active Equipment'), queryset.filter(status='operational').count()],
        [_('Under Maintenance'), queryset.filter(status='maintenance').count()],
        [_('Faulty'), queryset.filter(status='faulty').count()],
    ]
    
    summary_table = Table(summary_data, colWidths=[200, 200])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOX', (0, 0), (-1, -1), 1, colors.black),
    ]))
    
    elements.append(summary_table)
    elements.append(Spacer(1, 30))
    
    # Equipment details
    if queryset.exists():
        # Table header
        data = [[
            _('Name'),
            _('Type'),
            _('Model'),
            _('Status'),
            _('Site'),
            _('Installation Date'),
        ]]
        
        # Table rows
        for eq in queryset:
            data.append([
                eq.name,
                eq.get_equipment_type_display(),
                eq.model,
                eq.get_status_display(),
                str(eq.site),
                eq.installation_date.strftime('%Y-%m-%d') if eq.installation_date else '',
            ])
        
        # Create the table
        col_widths = [120, 80, 100, 80, 120, 80]
        table = Table(data, colWidths=col_widths, repeatRows=1)
        
        # Style the table
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        elements.append(table)
    
    # Build the PDF
    doc.build(elements)
    
    return filepath


def _generate_equipment_excel(queryset, filepath):
    """Generate an Excel report of equipment."""
    full_path = os.path.join(settings.MEDIA_ROOT, filepath)
    
    # Create a workbook and add a worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = _('Equipment Report')
    
    # Header
    ws.merge_cells('A1:F1')
    ws['A1'] = _('Equipment Report')
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # Subtitle
    ws.merge_cells('A2:F2')
    ws['A2'] = _('Generated on {date}').format(date=timezone.now().strftime('%Y-%m-%d %H:%M:%S'))
    ws['A2'].alignment = Alignment(horizontal='center')
    
    # Summary
    ws['A4'] = _('Summary')
    ws['A4'].font = Font(bold=True)
    
    summary_data = [
        [_('Total Equipment'), len(queryset)],
        [_('Active Equipment'), queryset.filter(status='operational').count()],
        [_('Under Maintenance'), queryset.filter(status='maintenance').count()],
        [_('Faulty'), queryset.filter(status='faulty').count()],
    ]
    
    for i, (label, value) in enumerate(summary_data, 5):
        ws[f'A{i}'] = label
        ws[f'B{i}'] = value
    
    # Equipment details
    ws['A10'] = _('Equipment Details')
    ws['A10'].font = Font(bold=True)
    
    # Table header
    headers = [
        _('Name'),
        _('Type'),
        _('Model'),
        _('Status'),
        _('Site'),
        _('Installation Date'),
    ]
    
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=11, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='DDDDDD', end_color='DDDDDD', fill_type='solid')
    
    # Table rows
    for row_num, eq in enumerate(queryset, 12):
        ws.cell(row=row_num, column=1, value=eq.name)
        ws.cell(row=row_num, column=2, value=eq.get_equipment_type_display())
        ws.cell(row=row_num, column=3, value=eq.model)
        ws.cell(row=row_num, column=4, value=eq.get_status_display())
        ws.cell(row=row_num, column=5, value=str(eq.site))
        if eq.installation_date:
            ws.cell(row=row_num, column=6, value=eq.installation_date.strftime('%Y-%m-%d'))
    
    # Auto-adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2) * 1.2
        ws.column_dimensions[column].width = min(adjusted_width, 50)
    
    # Save the workbook
    wb.save(full_path)
    
    return filepath


def _generate_equipment_csv(queryset, filepath):
    """Generate a CSV report of equipment."""
    full_path = os.path.join(settings.MEDIA_ROOT, filepath)
    
    fieldnames = [
        'name',
        'equipment_type',
        'model',
        'manufacturer',
        'serial_number',
        'status',
        'site',
        'location',
        'installation_date',
        'warranty_expiry',
        'ip_address',
        'mac_address',
    ]
    
    with open(full_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for eq in queryset:
            writer.writerow({
                'name': eq.name,
                'equipment_type': eq.get_equipment_type_display(),
                'model': eq.model,
                'manufacturer': eq.manufacturer,
                'serial_number': eq.serial_number,
                'status': eq.get_status_display(),
                'site': str(eq.site),
                'location': eq.location,
                'installation_date': eq.installation_date.strftime('%Y-%m-%d') if eq.installation_date else '',
                'warranty_expiry': eq.warranty_expiry.strftime('%Y-%m-%d') if eq.warranty_expiry else '',
                'ip_address': eq.ip_address or '',
                'mac_address': eq.mac_address or '',
            })
    
    return filepath


def get_equipment_status_chart_data():
    """Get data for equipment status chart."""
    from .models import Equipment
    
    statuses = dict(Equipment._meta.get_field('status').choices)
    data = Equipment.objects.values('status').annotate(count=models.Count('id')).order_by('status')
    
    labels = [statuses[item['status']] for item in data]
    values = [item['count'] for item in data]
    
    return {
        'labels': json.dumps(labels),
        'values': json.dumps(values),
    }


def send_maintenance_reminders():
    """Send email reminders for upcoming maintenance."""
    from django.core.mail import send_mail
    from django.conf import settings
    from django.template.loader import render_to_string
    from django.utils import timezone
    from .models import Equipment
    
    # Find equipment with maintenance due in the next 7 days
    today = timezone.now().date()
    next_week = today + timezone.timedelta(days=7)
    
    equipment_due = Equipment.objects.filter(
        next_maintenance__isnull=False,
        next_maintenance__gte=today,
        next_maintenance__lte=next_week
    ).order_by('next_maintenance')
    
    if not equipment_due.exists():
        return False
    
    # Prepare email content
    context = {
        'equipment_list': equipment_due,
        'today': today,
        'next_week': next_week,
    }
    
    subject = _('Upcoming Equipment Maintenance Reminder')
    message = render_to_string('emails/maintenance_reminder.txt', context)
    html_message = render_to_string('emails/maintenance_reminder.html', context)
    
    # Send email to administrators
    admin_emails = [admin[1] for admin in settings.ADMINS]
    
    send_mail(
        subject=subject,
        message=message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=admin_emails,
        html_message=html_message,
        fail_silently=False,
    )
    
    return True
