from django import forms
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timed<PERSON>ta

from .models import Site, Equipment, MaintenanceLog, Incident, Document

User = get_user_model()


class SiteForm(forms.ModelForm):
    class Meta:
        model = Site
        fields = '__all__'
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'


class EquipmentForm(forms.ModelForm):
    class Meta:
        model = Equipment
        exclude = ('created_at', 'updated_at')
        widgets = {
            'installation_date': forms.DateInput(attrs={'type': 'date'}),
            'warranty_expiry': forms.DateInput(attrs={'type': 'date'}),
            'last_maintenance': forms.DateInput(attrs={'type': 'date'}),
            'next_maintenance': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            if field_name != 'is_active':
                field.widget.attrs['class'] = 'form-control'
    
    def clean(self):
        cleaned_data = super().clean()
        installation_date = cleaned_data.get('installation_date')
        warranty_expiry = cleaned_data.get('warranty_expiry')
        
        if installation_date and warranty_expiry and installation_date > warranty_expiry:
            self.add_error('warranty_expiry', 'Warranty expiry date must be after installation date.')
        
        return cleaned_data


class MaintenanceLogForm(forms.ModelForm):
    class Meta:
        model = MaintenanceLog
        exclude = ('created_at', 'updated_at', 'performed_by')
        widgets = {
            'performed_date': forms.DateInput(attrs={'type': 'date'}),
            'next_maintenance_date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
            'actions_taken': forms.Textarea(attrs={'rows': 3}),
            'parts_replaced': forms.Textarea(attrs={'rows': 2}),
        }
    
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
    
    def save(self, commit=True):
        maintenance_log = super().save(commit=False)
        if not maintenance_log.pk:  # Only set added during the first save.
            maintenance_log.performed_by = self.request.user if self.request else None
        if commit:
            maintenance_log.save()
        return maintenance_log


class IncidentForm(forms.ModelForm):
    class Meta:
        model = Incident
        exclude = ('created_at', 'updated_at', 'reported_by', 'resolved_date')
        widgets = {
            'reported_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'resolved_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}, format='%Y-%m-%dT%H:%M'),
            'description': forms.Textarea(attrs={'rows': 4}),
            'resolution': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            if field_name != 'equipment':
                field.widget.attrs['class'] = 'form-control'
        
        # Customize the equipment field to show only equipment from the selected site
        if 'site' in self.data and self.data['site']:
            self.fields['equipment'].queryset = Equipment.objects.filter(site_id=self.data['site'])
        elif self.instance.pk and self.instance.site:
            self.fields['equipment'].queryset = self.instance.site.equipment_set.all()
        else:
            self.fields['equipment'].queryset = Equipment.objects.none()
    
    def save(self, commit=True):
        incident = super().save(commit=False)
        if not incident.pk:  # Only set added during the first save.
            incident.reported_by = self.request.user if self.request else None
        
        # If status is being changed to resolved, set the resolved_date
        if 'status' in self.changed_data and self.cleaned_data['status'] == 'resolved' and not incident.resolved_date:
            incident.resolved_date = timezone.now()
        
        if commit:
            incident.save()
            self.save_m2m()
        return incident


class DocumentForm(forms.ModelForm):
    class Meta:
        model = Document
        exclude = ('uploaded_by', 'upload_date')
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
    
    def clean(self):
        cleaned_data = super().clean()
        site = cleaned_data.get('site')
        equipment = cleaned_data.get('equipment')
        
        if site and equipment:
            raise ValidationError('A document can only be associated with either a site or equipment, not both.')
        if not site and not equipment:
            raise ValidationError('A document must be associated with either a site or equipment.')
        
        return cleaned_data
    
    def save(self, commit=True):
        document = super().save(commit=False)
        if not document.pk:  # Only set added during the first save.
            document.uploaded_by = self.request.user if self.request else None
        if commit:
            document.save()
        return document


class SiteFilterForm(forms.Form):
    STATUS_CHOICES = [
        ('', 'All Status'),
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    search = forms.CharField(required=False, widget=forms.TextInput(attrs={
        'class': 'form-control',
        'placeholder': 'Search by name, city, or address...',
    }))
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    country = forms.CharField(required=False, widget=forms.TextInput(attrs={
        'class': 'form-control',
        'placeholder': 'Filter by country...',
    }))


class EquipmentFilterForm(forms.Form):
    STATUS_CHOICES = [
        ('', 'All Status'),
        ('operational', 'Operational'),
        ('maintenance', 'Under Maintenance'),
        ('faulty', 'Faulty'),
        ('decommissioned', 'Decommissioned'),
    ]
    
    search = forms.CharField(required=False, widget=forms.TextInput(attrs={
        'class': 'form-control',
        'placeholder': 'Search by name, model, or serial number...',
    }))
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    equipment_type = forms.ChoiceField(
        choices=[('', 'All Types')] + Equipment.EQUIPMENT_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    site = forms.ModelChoiceField(
        queryset=Site.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limit sites to those with equipment
        self.fields['site'].queryset = Site.objects.filter(equipment__isnull=False).distinct()
